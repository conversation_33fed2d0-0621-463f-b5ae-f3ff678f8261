{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/app/globals.css"], "sourcesContent": ["/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */\n@layer properties;\n@layer theme, base, components, utilities;\n@layer theme {\n  :root, :host {\n    --color-blue-50: oklch(97% 0.014 254.604);\n    --color-blue-600: oklch(54.6% 0.245 262.881);\n    --color-blue-700: oklch(48.8% 0.243 264.376);\n    --color-indigo-100: oklch(93% 0.034 272.788);\n    --color-gray-600: oklch(44.6% 0.03 256.802);\n    --color-gray-900: oklch(21% 0.034 264.665);\n    --color-white: #fff;\n    --spacing: 0.25rem;\n    --container-2xl: 42rem;\n    --container-4xl: 56rem;\n    --text-sm: 0.875rem;\n    --text-sm--line-height: calc(1.25 / 0.875);\n    --text-lg: 1.125rem;\n    --text-lg--line-height: calc(1.75 / 1.125);\n    --text-xl: 1.25rem;\n    --text-xl--line-height: calc(1.75 / 1.25);\n    --text-2xl: 1.5rem;\n    --text-2xl--line-height: calc(2 / 1.5);\n    --text-4xl: 2.25rem;\n    --text-4xl--line-height: calc(2.5 / 2.25);\n    --text-6xl: 3.75rem;\n    --text-6xl--line-height: 1;\n    --font-weight-medium: 500;\n    --font-weight-semibold: 600;\n    --font-weight-bold: 700;\n    --radius-lg: 0.5rem;\n    --radius-xl: 0.75rem;\n    --blur-xl: 24px;\n    --default-transition-duration: 150ms;\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    --default-font-family: var(--font-geist-sans);\n    --default-mono-font-family: var(--font-geist-mono);\n    --gradient-primary: var(--gradient-primary);\n    --shadow-soft: var(--shadow-soft);\n    --shadow-medium: var(--shadow-medium);\n    --shadow-accent: var(--shadow-accent);\n  }\n}\n@layer base {\n  *, ::after, ::before, ::backdrop, ::file-selector-button {\n    box-sizing: border-box;\n    margin: 0;\n    padding: 0;\n    border: 0 solid;\n  }\n  html, :host {\n    line-height: 1.5;\n    -webkit-text-size-adjust: 100%;\n    tab-size: 4;\n    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\");\n    font-feature-settings: var(--default-font-feature-settings, normal);\n    font-variation-settings: var(--default-font-variation-settings, normal);\n    -webkit-tap-highlight-color: transparent;\n  }\n  hr {\n    height: 0;\n    color: inherit;\n    border-top-width: 1px;\n  }\n  abbr:where([title]) {\n    -webkit-text-decoration: underline dotted;\n    text-decoration: underline dotted;\n  }\n  h1, h2, h3, h4, h5, h6 {\n    font-size: inherit;\n    font-weight: inherit;\n  }\n  a {\n    color: inherit;\n    -webkit-text-decoration: inherit;\n    text-decoration: inherit;\n  }\n  b, strong {\n    font-weight: bolder;\n  }\n  code, kbd, samp, pre {\n    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace);\n    font-feature-settings: var(--default-mono-font-feature-settings, normal);\n    font-variation-settings: var(--default-mono-font-variation-settings, normal);\n    font-size: 1em;\n  }\n  small {\n    font-size: 80%;\n  }\n  sub, sup {\n    font-size: 75%;\n    line-height: 0;\n    position: relative;\n    vertical-align: baseline;\n  }\n  sub {\n    bottom: -0.25em;\n  }\n  sup {\n    top: -0.5em;\n  }\n  table {\n    text-indent: 0;\n    border-color: inherit;\n    border-collapse: collapse;\n  }\n  :-moz-focusring {\n    outline: auto;\n  }\n  progress {\n    vertical-align: baseline;\n  }\n  summary {\n    display: list-item;\n  }\n  ol, ul, menu {\n    list-style: none;\n  }\n  img, svg, video, canvas, audio, iframe, embed, object {\n    display: block;\n    vertical-align: middle;\n  }\n  img, video {\n    max-width: 100%;\n    height: auto;\n  }\n  button, input, select, optgroup, textarea, ::file-selector-button {\n    font: inherit;\n    font-feature-settings: inherit;\n    font-variation-settings: inherit;\n    letter-spacing: inherit;\n    color: inherit;\n    border-radius: 0;\n    background-color: transparent;\n    opacity: 1;\n  }\n  :where(select:is([multiple], [size])) optgroup {\n    font-weight: bolder;\n  }\n  :where(select:is([multiple], [size])) optgroup option {\n    padding-inline-start: 20px;\n  }\n  ::file-selector-button {\n    margin-inline-end: 4px;\n  }\n  ::placeholder {\n    opacity: 1;\n  }\n  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {\n    ::placeholder {\n      color: currentcolor;\n      @supports (color: color-mix(in lab, red, red)) {\n        color: color-mix(in oklab, currentcolor 50%, transparent);\n      }\n    }\n  }\n  textarea {\n    resize: vertical;\n  }\n  ::-webkit-search-decoration {\n    -webkit-appearance: none;\n  }\n  ::-webkit-date-and-time-value {\n    min-height: 1lh;\n    text-align: inherit;\n  }\n  ::-webkit-datetime-edit {\n    display: inline-flex;\n  }\n  ::-webkit-datetime-edit-fields-wrapper {\n    padding: 0;\n  }\n  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {\n    padding-block: 0;\n  }\n  :-moz-ui-invalid {\n    box-shadow: none;\n  }\n  button, input:where([type=\"button\"], [type=\"reset\"], [type=\"submit\"]), ::file-selector-button {\n    appearance: button;\n  }\n  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {\n    height: auto;\n  }\n  [hidden]:where(:not([hidden=\"until-found\"])) {\n    display: none !important;\n  }\n}\n@layer utilities {\n  .visible {\n    visibility: visible;\n  }\n  .absolute {\n    position: absolute;\n  }\n  .fixed {\n    position: fixed;\n  }\n  .relative {\n    position: relative;\n  }\n  .inset-0 {\n    inset: calc(var(--spacing) * 0);\n  }\n  .right-0 {\n    right: calc(var(--spacing) * 0);\n  }\n  .z-40 {\n    z-index: 40;\n  }\n  .z-50 {\n    z-index: 50;\n  }\n  .container {\n    width: 100%;\n    @media (width >= 40rem) {\n      max-width: 40rem;\n    }\n    @media (width >= 48rem) {\n      max-width: 48rem;\n    }\n    @media (width >= 64rem) {\n      max-width: 64rem;\n    }\n    @media (width >= 80rem) {\n      max-width: 80rem;\n    }\n    @media (width >= 96rem) {\n      max-width: 96rem;\n    }\n  }\n  .mx-auto {\n    margin-inline: auto;\n  }\n  .mt-2 {\n    margin-top: calc(var(--spacing) * 2);\n  }\n  .mr-3 {\n    margin-right: calc(var(--spacing) * 3);\n  }\n  .mb-4 {\n    margin-bottom: calc(var(--spacing) * 4);\n  }\n  .mb-6 {\n    margin-bottom: calc(var(--spacing) * 6);\n  }\n  .mb-8 {\n    margin-bottom: calc(var(--spacing) * 8);\n  }\n  .mb-12 {\n    margin-bottom: calc(var(--spacing) * 12);\n  }\n  .block {\n    display: block;\n  }\n  .flex {\n    display: flex;\n  }\n  .hidden {\n    display: none;\n  }\n  .inline-block {\n    display: inline-block;\n  }\n  .h-2 {\n    height: calc(var(--spacing) * 2);\n  }\n  .h-4 {\n    height: calc(var(--spacing) * 4);\n  }\n  .min-h-screen {\n    min-height: 100vh;\n  }\n  .w-2 {\n    width: calc(var(--spacing) * 2);\n  }\n  .w-4 {\n    width: calc(var(--spacing) * 4);\n  }\n  .w-52 {\n    width: calc(var(--spacing) * 52);\n  }\n  .w-full {\n    width: 100%;\n  }\n  .max-w-2xl {\n    max-width: var(--container-2xl);\n  }\n  .max-w-4xl {\n    max-width: var(--container-4xl);\n  }\n  .flex-1 {\n    flex: 1;\n  }\n  .rotate-180 {\n    rotate: 180deg;\n  }\n  .items-center {\n    align-items: center;\n  }\n  .space-y-3 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-x-2 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-3 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .overflow-hidden {\n    overflow: hidden;\n  }\n  .rounded-full {\n    border-radius: calc(infinity * 1px);\n  }\n  .rounded-lg {\n    border-radius: var(--radius-lg);\n  }\n  .rounded-xl {\n    border-radius: var(--radius-xl);\n  }\n  .border {\n    border-style: var(--tw-border-style);\n    border-width: 1px;\n  }\n  .border-t {\n    border-top-style: var(--tw-border-style);\n    border-top-width: 1px;\n  }\n  .border-l-2 {\n    border-left-style: var(--tw-border-style);\n    border-left-width: 2px;\n  }\n  .border-accent-red {\n    border-color: var(--accent-red);\n  }\n  .bg-accent-red\\/10 {\n    background-color: var(--accent-red);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--accent-red) 10%, transparent);\n    }\n  }\n  .bg-bg-primary {\n    background-color: var(--bg-primary);\n  }\n  .bg-bg-secondary {\n    background-color: var(--bg-secondary);\n  }\n  .bg-blue-600 {\n    background-color: var(--color-blue-600);\n  }\n  .bg-white {\n    background-color: var(--color-white);\n  }\n  .bg-gradient-to-br {\n    --tw-gradient-position: to bottom right in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .from-blue-50 {\n    --tw-gradient-from: var(--color-blue-50);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-indigo-100 {\n    --tw-gradient-to: var(--color-indigo-100);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .p-8 {\n    padding: calc(var(--spacing) * 8);\n  }\n  .px-4 {\n    padding-inline: calc(var(--spacing) * 4);\n  }\n  .px-6 {\n    padding-inline: calc(var(--spacing) * 6);\n  }\n  .py-2 {\n    padding-block: calc(var(--spacing) * 2);\n  }\n  .py-3 {\n    padding-block: calc(var(--spacing) * 3);\n  }\n  .py-8 {\n    padding-block: calc(var(--spacing) * 8);\n  }\n  .py-16 {\n    padding-block: calc(var(--spacing) * 16);\n  }\n  .pt-8 {\n    padding-top: calc(var(--spacing) * 8);\n  }\n  .text-center {\n    text-align: center;\n  }\n  .text-left {\n    text-align: left;\n  }\n  .text-2xl {\n    font-size: var(--text-2xl);\n    line-height: var(--tw-leading, var(--text-2xl--line-height));\n  }\n  .text-4xl {\n    font-size: var(--text-4xl);\n    line-height: var(--tw-leading, var(--text-4xl--line-height));\n  }\n  .text-lg {\n    font-size: var(--text-lg);\n    line-height: var(--tw-leading, var(--text-lg--line-height));\n  }\n  .text-sm {\n    font-size: var(--text-sm);\n    line-height: var(--tw-leading, var(--text-sm--line-height));\n  }\n  .text-xl {\n    font-size: var(--text-xl);\n    line-height: var(--tw-leading, var(--text-xl--line-height));\n  }\n  .font-bold {\n    --tw-font-weight: var(--font-weight-bold);\n    font-weight: var(--font-weight-bold);\n  }\n  .font-medium {\n    --tw-font-weight: var(--font-weight-medium);\n    font-weight: var(--font-weight-medium);\n  }\n  .font-semibold {\n    --tw-font-weight: var(--font-weight-semibold);\n    font-weight: var(--font-weight-semibold);\n  }\n  .text-accent-red {\n    color: var(--accent-red);\n  }\n  .text-gray-600 {\n    color: var(--color-gray-600);\n  }\n  .text-gray-900 {\n    color: var(--color-gray-900);\n  }\n  .text-text-muted {\n    color: var(--text-muted);\n  }\n  .text-text-secondary {\n    color: var(--text-secondary);\n  }\n  .text-white {\n    color: var(--color-white);\n  }\n  .shadow-md {\n    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .filter {\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .backdrop-blur-xl {\n    --tw-backdrop-blur: blur(var(--blur-xl));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .transition {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-all {\n    transition-property: all;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-colors {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-transform {\n    transition-property: transform, translate, scale, rotate;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .duration-200 {\n    --tw-duration: 200ms;\n    transition-duration: 200ms;\n  }\n  .duration-300 {\n    --tw-duration: 300ms;\n    transition-duration: 300ms;\n  }\n  .hover\\:border-accent-cyan {\n    &:hover {\n      @media (hover: hover) {\n        border-color: var(--accent-cyan);\n      }\n    }\n  }\n  .hover\\:bg-bg-secondary {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--bg-secondary);\n      }\n    }\n  }\n  .hover\\:bg-bg-tertiary {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--bg-tertiary);\n      }\n    }\n  }\n  .hover\\:bg-blue-700 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-blue-700);\n      }\n    }\n  }\n  .hover\\:text-text-primary {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--text-primary);\n      }\n    }\n  }\n  .md\\:text-6xl {\n    @media (width >= 48rem) {\n      font-size: var(--text-6xl);\n      line-height: var(--tw-leading, var(--text-6xl--line-height));\n    }\n  }\n}\n:root {\n  --bg-primary: #0a0a0a;\n  --bg-secondary: #111111;\n  --bg-tertiary: #1a1a1a;\n  --text-primary: #ffffff;\n  --text-secondary: #a1a1a1;\n  --text-muted: #666666;\n  --accent-red: #ff2e4d;\n  --accent-purple: #7c3aed;\n  --accent-cyan: #06b6d4;\n  --border-color: #222222;\n  --border-hover: #333333;\n  --gradient-primary: linear-gradient(\n    135deg,\n    #ff2e4d 0%,\n    #7c3aed 50%,\n    #06b6d4 100%\n  );\n  --shadow-soft: 0 4px 20px rgba(0, 0, 0, 0.3);\n  --shadow-medium: 0 8px 40px rgba(0, 0, 0, 0.4);\n  --shadow-accent: 0 10px 30px rgba(255, 46, 77, 0.2);\n  --background: var(--bg-primary);\n  --foreground: var(--text-primary);\n}\nbody {\n  background: var(--background);\n  color: var(--foreground);\n  font-family: Arial, Helvetica, sans-serif;\n}\n@keyframes float-1 {\n  0%,\n  100% {\n    transform: translateY(0px) rotate(0deg);\n  }\n  50% {\n    transform: translateY(-20px) rotate(2deg);\n  }\n}\n@keyframes float-2 {\n  0%,\n  100% {\n    transform: translateY(0px) rotate(0deg);\n  }\n  50% {\n    transform: translateY(-15px) rotate(-1deg);\n  }\n}\n@keyframes float-3 {\n  0%,\n  100% {\n    transform: translateY(0px) rotate(0deg);\n  }\n  50% {\n    transform: translateY(-25px) rotate(1deg);\n  }\n}\n@keyframes animate-in {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n.animate-float-1 {\n  animation: float-1 6s ease-in-out infinite;\n}\n.animate-float-2 {\n  animation: float-2 8s ease-in-out infinite;\n}\n.animate-float-3 {\n  animation: float-3 7s ease-in-out infinite;\n}\n.animate-in {\n  animation: animate-in 0.6s ease-out forwards;\n}\n.feature-card {\n  transition: all 0.3s ease;\n}\n.feature-card:hover {\n  transform: translateY(-4px);\n}\n.screenshot-item {\n  opacity: 0;\n  transform: translateY(30px);\n  transition: all 0.6s ease;\n}\n.screenshot-item.animate-in {\n  opacity: 1;\n  transform: translateY(0);\n}\n.hero {\n  padding: 120px 0;\n  position: relative;\n  overflow: hidden;\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 80px;\n  align-items: center;\n  min-height: 100vh;\n}\n.hero-content {\n  max-width: 600px;\n}\n.hero-badge {\n  display: inline-block;\n  background: rgba(255, 46, 77, 0.1);\n  color: var(--accent-red);\n  padding: 12px 24px;\n  border-radius: 50px;\n  font-size: 12px;\n  font-weight: 700;\n  letter-spacing: 1px;\n  text-transform: uppercase;\n  margin-bottom: 30px;\n  border: 1px solid rgba(255, 46, 77, 0.2);\n}\n.hero-title {\n  font-size: 4rem;\n  font-weight: 900;\n  line-height: 1.1;\n  margin-bottom: 30px;\n  color: var(--text-primary);\n}\n.hero-subtitle {\n  font-size: 1.25rem;\n  color: var(--text-secondary);\n  line-height: 1.6;\n  margin-bottom: 40px;\n  max-width: 500px;\n}\n.hero-actions {\n  display: flex;\n  gap: 20px;\n  margin-bottom: 60px;\n  flex-wrap: wrap;\n}\n.hero-stats {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 40px;\n  max-width: 400px;\n}\n.stat {\n  text-align: center;\n}\n.stat-number {\n  display: block;\n  font-size: 2rem;\n  font-weight: 900;\n  margin-bottom: 8px;\n}\n.stat:nth-child(1) .stat-number {\n  color: var(--accent-red);\n}\n.stat:nth-child(2) .stat-number {\n  color: var(--accent-purple);\n}\n.stat:nth-child(3) .stat-number {\n  color: var(--accent-cyan);\n}\n.stat-label {\n  font-size: 0.875rem;\n  color: var(--text-muted);\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n.hero-visual {\n  position: relative;\n  height: 500px;\n}\n.hero-mockup {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.floating-card {\n  position: absolute;\n  background: var(--bg-secondary);\n  border: 1px solid var(--border-color);\n  border-radius: 12px;\n  padding: 20px;\n  box-shadow: var(--shadow-medium);\n}\n.floating-card.card-1 {\n  top: 20%;\n  left: 10%;\n}\n.floating-card.card-2 {\n  top: 10%;\n  right: 20%;\n}\n.floating-card.card-3 {\n  bottom: 20%;\n  left: 20%;\n}\n.persona-preview {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n.persona-preview img {\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n}\n.persona-preview h4 {\n  font-size: 1rem;\n  font-weight: 700;\n  color: var(--text-primary);\n  margin-bottom: 4px;\n}\n.persona-preview p {\n  font-size: 0.875rem;\n  color: var(--text-secondary);\n}\n.platform-icons {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 12px;\n}\n.source-icon {\n  width: 40px;\n  height: 40px;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: var(--text-secondary);\n  transition: all 0.3s ease;\n}\n.source-icon:hover {\n  background: rgba(255, 46, 77, 0.1);\n  color: var(--accent-red);\n}\n.content-preview {\n  max-width: 250px;\n}\n.preview-image {\n  margin-bottom: 15px;\n}\n.content-preview p {\n  font-size: 0.875rem;\n  color: var(--text-primary);\n  margin-bottom: 8px;\n  line-height: 1.4;\n}\n.preview-meta {\n  font-size: 0.75rem;\n  color: var(--accent-cyan);\n  font-weight: 500;\n}\n.gradient-text {\n  background: var(--gradient-primary);\n  background-clip: text;\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-size: 200% 200%;\n  animation: gradientShift 4s ease infinite;\n}\n@keyframes gradientShift {\n  0% {\n    background-position: 0% 50%;\n  }\n  50% {\n    background-position: 100% 50%;\n  }\n  100% {\n    background-position: 0% 50%;\n  }\n}\n.btn-primary,\n.btn-secondary {\n  padding: 12px 24px;\n  border-radius: 8px;\n  font-weight: 600;\n  font-size: 14px;\n  border: none;\n  cursor: pointer;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  text-decoration: none;\n  display: inline-block;\n}\n.btn-primary {\n  background: var(--gradient-primary);\n  color: white;\n  box-shadow: var(--shadow-accent);\n}\n.btn-primary:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 15px 40px rgba(255, 46, 77, 0.3);\n}\n.btn-secondary {\n  background: transparent;\n  color: var(--text-primary);\n  border: 1px solid var(--border-color);\n}\n.btn-secondary:hover {\n  border-color: var(--accent-red);\n  background: rgba(255, 46, 77, 0.1);\n}\n.btn-large {\n  padding: 16px 32px;\n  font-size: 16px;\n}\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 20px;\n}\n@media (max-width: 1024px) {\n  .hero {\n    grid-template-columns: 1fr;\n    gap: 60px;\n    text-align: center;\n  }\n  .hero-title {\n    font-size: 3rem;\n  }\n}\n@media (max-width: 768px) {\n  .hero {\n    padding: 80px 0;\n  }\n  .hero-title {\n    font-size: 2.5rem;\n  }\n  .hero-stats {\n    grid-template-columns: 1fr;\n    gap: 20px;\n  }\n  .hero-actions {\n    flex-direction: column;\n    align-items: center;\n  }\n  .floating-card {\n    position: relative !important;\n    top: auto !important;\n    left: auto !important;\n    right: auto !important;\n    bottom: auto !important;\n    margin-bottom: 20px;\n  }\n  .hero-visual {\n    height: auto;\n  }\n}\n.navbar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: rgba(10, 10, 10, 0.9);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid var(--border-color);\n  z-index: 1000;\n  padding: 16px 0;\n}\n.navbar .container {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n.nav-logo {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n.logo-icon {\n  width: 40px;\n  height: 40px;\n  position: relative;\n}\n.logo-icon.small {\n  width: 32px;\n  height: 32px;\n}\n.persona-layers {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.persona-layer {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);\n}\n.layer-1 {\n  background: linear-gradient(135deg, var(--accent-red), #ff6b7a);\n  animation: rotate1 8s linear infinite;\n}\n.layer-2 {\n  background: linear-gradient(135deg, var(--accent-purple), #a855f7);\n  transform: scale(0.85) rotate(120deg);\n  animation: rotate2 10s linear infinite reverse;\n  opacity: 0.8;\n}\n.layer-3 {\n  background: linear-gradient(135deg, var(--accent-cyan), #22d3ee);\n  transform: scale(0.7) rotate(240deg);\n  animation: rotate3 12s linear infinite;\n  opacity: 0.6;\n}\n@keyframes rotate1 {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n@keyframes rotate2 {\n  from {\n    transform: scale(0.85) rotate(120deg);\n  }\n  to {\n    transform: scale(0.85) rotate(480deg);\n  }\n}\n@keyframes rotate3 {\n  from {\n    transform: scale(0.7) rotate(240deg);\n  }\n  to {\n    transform: scale(0.7) rotate(600deg);\n  }\n}\n.logo-text {\n  font-size: 20px;\n  font-weight: 900;\n  color: var(--text-primary);\n}\n.features {\n  padding: 120px 0;\n  background: var(--bg-primary);\n}\n.section-header {\n  text-align: center;\n  margin-bottom: 80px;\n}\n.section-header h2 {\n  font-size: 3.5rem;\n  font-weight: 900;\n  color: var(--text-primary);\n  margin-bottom: 20px;\n  line-height: 1.1;\n}\n.section-header p {\n  font-size: 1.25rem;\n  color: var(--text-secondary);\n  max-width: 600px;\n  margin: 0 auto;\n}\n.features-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n  gap: 40px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n.feature-card {\n  background: var(--bg-secondary);\n  border: 1px solid var(--border-color);\n  border-radius: 20px;\n  padding: 40px;\n  text-align: center;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n.feature-card::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 1px;\n  background: var(--gradient-primary);\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n.feature-card:hover {\n  transform: translateY(-8px);\n  border-color: var(--border-hover);\n  box-shadow: var(--shadow-medium);\n}\n.feature-card:hover::before {\n  opacity: 1;\n}\n.feature-icon {\n  margin-bottom: 30px;\n  display: flex;\n  justify-content: center;\n}\n.feature-card h3 {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: var(--text-primary);\n  margin-bottom: 20px;\n}\n.feature-card p {\n  color: var(--text-secondary);\n  line-height: 1.6;\n  font-size: 1rem;\n}\n@media (max-width: 768px) {\n  .features {\n    padding: 80px 0;\n  }\n  .section-header h2 {\n    font-size: 2.5rem;\n  }\n  .features-grid {\n    grid-template-columns: 1fr;\n    gap: 30px;\n  }\n  .feature-card {\n    padding: 30px;\n  }\n}\n.cta {\n  padding: 120px 0;\n  background: linear-gradient(\n    135deg,\n    var(--bg-secondary) 0%,\n    var(--bg-primary) 100%\n  );\n  position: relative;\n  overflow: hidden;\n}\n.cta::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: radial-gradient(\n      circle at 30% 20%,\n      rgba(255, 46, 77, 0.1) 0%,\n      transparent 50%\n    ),\n    radial-gradient(\n      circle at 70% 80%,\n      rgba(124, 58, 237, 0.1) 0%,\n      transparent 50%\n    );\n  pointer-events: none;\n}\n.cta-content {\n  text-align: center;\n  max-width: 800px;\n  margin: 0 auto;\n  position: relative;\n  z-index: 1;\n}\n.cta-content h2 {\n  font-size: 3.5rem;\n  font-weight: 900;\n  color: var(--text-primary);\n  margin-bottom: 20px;\n  line-height: 1.1;\n}\n.cta-content > p {\n  font-size: 1.25rem;\n  color: var(--text-secondary);\n  margin-bottom: 50px;\n  line-height: 1.6;\n}\n.cta-form {\n  max-width: 500px;\n  margin: 0 auto;\n}\n.form-group {\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n}\n.email-input {\n  flex: 1;\n  padding: 18px 24px;\n  border: 2px solid var(--border-color);\n  border-radius: 12px;\n  background: var(--bg-primary);\n  color: var(--text-primary);\n  font-size: 1rem;\n  transition: all 0.3s ease;\n}\n.email-input:focus {\n  outline: none;\n  border-color: var(--accent-red);\n  box-shadow: 0 0 0 3px rgba(255, 46, 77, 0.1);\n}\n.email-input::placeholder {\n  color: var(--text-secondary);\n}\n.btn-large {\n  padding: 18px 32px;\n  font-size: 1rem;\n  font-weight: 600;\n  white-space: nowrap;\n}\n.form-note {\n  color: var(--text-secondary);\n  font-size: 0.9rem;\n  margin-top: 15px;\n}\n.success-message {\n  text-align: center;\n  padding: 40px;\n  background: var(--bg-secondary);\n  border: 1px solid var(--border-color);\n  border-radius: 20px;\n  max-width: 400px;\n  margin: 0 auto;\n}\n.success-icon {\n  font-size: 3rem;\n  margin-bottom: 20px;\n}\n.success-message h3 {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: var(--text-primary);\n  margin-bottom: 10px;\n}\n.success-message p {\n  color: var(--text-secondary);\n  margin: 0;\n}\n@media (max-width: 768px) {\n  .cta {\n    padding: 80px 0;\n  }\n  .cta-content h2 {\n    font-size: 2.5rem;\n  }\n  .form-group {\n    flex-direction: column;\n    gap: 15px;\n  }\n  .btn-large {\n    width: 100%;\n  }\n}\n.footer {\n  background: var(--bg-secondary);\n  border-top: 1px solid var(--border-color);\n  padding: 80px 0 40px;\n}\n.footer-content {\n  max-width: 1200px;\n  margin: 0 auto;\n}\n.footer-main {\n  display: grid;\n  grid-template-columns: 1fr 2fr;\n  gap: 80px;\n  margin-bottom: 60px;\n}\n.footer-brand {\n  max-width: 400px;\n}\n.brand-description {\n  color: var(--text-secondary);\n  line-height: 1.6;\n  margin-top: 20px;\n}\n.footer-links {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 40px;\n}\n.link-group h4 {\n  color: var(--text-primary);\n  font-weight: 600;\n  margin-bottom: 20px;\n  font-size: 1rem;\n}\n.link-group ul {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n.link-group li {\n  margin-bottom: 12px;\n}\n.link-group a {\n  color: var(--text-secondary);\n  text-decoration: none;\n  transition: color 0.3s ease;\n  font-size: 0.9rem;\n}\n.link-group a:hover {\n  color: var(--text-primary);\n}\n.footer-bottom {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-top: 40px;\n  border-top: 1px solid var(--border-color);\n}\n.footer-legal {\n  display: flex;\n  align-items: center;\n  gap: 30px;\n}\n.footer-legal p {\n  color: var(--text-secondary);\n  margin: 0;\n  font-size: 0.9rem;\n}\n.legal-links {\n  display: flex;\n  gap: 20px;\n}\n.legal-links a {\n  color: var(--text-secondary);\n  text-decoration: none;\n  font-size: 0.9rem;\n  transition: color 0.3s ease;\n}\n.legal-links a:hover {\n  color: var(--text-primary);\n}\n.footer-social {\n  display: flex;\n  gap: 15px;\n}\n.social-link {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  border: 1px solid var(--border-color);\n  border-radius: 8px;\n  color: var(--text-secondary);\n  text-decoration: none;\n  transition: all 0.3s ease;\n}\n.social-link:hover {\n  color: var(--text-primary);\n  border-color: var(--accent-red);\n  background: var(--accent-red) / 10;\n}\n@media (max-width: 768px) {\n  .footer {\n    padding: 60px 0 30px;\n  }\n  .footer-main {\n    grid-template-columns: 1fr;\n    gap: 40px;\n    margin-bottom: 40px;\n  }\n  .footer-links {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 30px;\n  }\n  .footer-bottom {\n    flex-direction: column;\n    gap: 20px;\n    text-align: center;\n  }\n  .footer-legal {\n    flex-direction: column;\n    gap: 15px;\n  }\n  .legal-links {\n    justify-content: center;\n  }\n}\n.demo {\n  padding: 120px 0;\n  background: var(--bg-primary);\n}\n.demo-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n  gap: 30px;\n  max-width: 1400px;\n  margin: 0 auto;\n}\n.demo-card {\n  background: var(--bg-secondary);\n  border: 1px solid var(--border-color);\n  border-radius: 16px;\n  overflow: hidden;\n  transition: all 0.3s ease;\n}\n.demo-card:hover {\n  transform: translateY(-4px);\n  box-shadow: var(--shadow-medium);\n  border-color: var(--border-hover);\n}\n.demo-header {\n  padding: 20px 24px;\n  border-bottom: 1px solid var(--border-color);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n.demo-header h3 {\n  font-size: 1.1rem;\n  font-weight: 600;\n  color: var(--text-primary);\n  margin: 0;\n}\n.demo-status {\n  padding: 4px 12px;\n  background: var(--accent-red) / 10;\n  color: var(--accent-red);\n  border-radius: 20px;\n  font-size: 0.8rem;\n  font-weight: 500;\n}\n.demo-content {\n  padding: 24px;\n}\n.photo-grid {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 12px;\n  margin-bottom: 20px;\n}\n.photo-item {\n  aspect-ratio: 1;\n  background: var(--bg-primary);\n  border: 1px solid var(--border-color);\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.5rem;\n}\n.analysis-results {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n.insight {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px 0;\n}\n.insight-label {\n  color: var(--text-secondary);\n  font-size: 0.9rem;\n}\n.insight-value {\n  color: var(--text-primary);\n  font-weight: 500;\n  font-size: 0.9rem;\n}\n.persona-list {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n.persona-item {\n  display: flex;\n  gap: 16px;\n  padding: 16px;\n  border: 1px solid var(--border-color);\n  border-radius: 12px;\n  transition: all 0.3s ease;\n}\n.persona-item.active {\n  border-color: var(--accent-red);\n  background: var(--accent-red) / 5;\n}\n.persona-item:hover {\n  border-color: var(--border-hover);\n}\n.persona-avatar {\n  width: 48px;\n  height: 48px;\n  background: var(--bg-primary);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.5rem;\n  flex-shrink: 0;\n}\n.persona-info h4 {\n  font-size: 1rem;\n  font-weight: 600;\n  color: var(--text-primary);\n  margin: 0 0 4px 0;\n}\n.persona-info p {\n  font-size: 0.85rem;\n  color: var(--text-secondary);\n  margin: 0;\n  line-height: 1.4;\n}\n.generated-post {\n  border: 1px solid var(--border-color);\n  border-radius: 12px;\n  overflow: hidden;\n}\n.post-header {\n  padding: 16px;\n  border-bottom: 1px solid var(--border-color);\n  display: flex;\n  gap: 12px;\n  align-items: center;\n}\n.post-avatar {\n  width: 40px;\n  height: 40px;\n  background: var(--bg-primary);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.2rem;\n}\n.post-info h4 {\n  font-size: 0.9rem;\n  font-weight: 600;\n  color: var(--text-primary);\n  margin: 0;\n}\n.post-info span {\n  font-size: 0.8rem;\n  color: var(--text-secondary);\n}\n.post-content {\n  padding: 16px;\n}\n.post-content p {\n  color: var(--text-primary);\n  line-height: 1.5;\n  margin: 0 0 16px 0;\n  font-size: 0.9rem;\n}\n.post-stats {\n  display: flex;\n  gap: 20px;\n}\n.post-stats span {\n  color: var(--text-secondary);\n  font-size: 0.8rem;\n}\n@media (max-width: 768px) {\n  .demo {\n    padding: 80px 0;\n  }\n  .demo-grid {\n    grid-template-columns: 1fr;\n    gap: 20px;\n  }\n  .photo-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n.icon-personas {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.mini-persona {\n  position: absolute;\n  width: 20px;\n  height: 20px;\n  border-radius: 50%;\n  border: 2px solid var(--bg-primary);\n}\n.mini-persona.p1 {\n  background: var(--accent-red);\n  top: 0;\n  left: 20px;\n}\n.mini-persona.p2 {\n  background: var(--accent-purple);\n  top: 20px;\n  left: 0;\n}\n.mini-persona.p3 {\n  background: var(--accent-cyan);\n  top: 20px;\n  right: 0;\n}\n.icon-camera {\n  position: relative;\n  width: 50px;\n  height: 40px;\n  background: var(--bg-secondary);\n  border-radius: 8px;\n  border: 2px solid var(--accent-cyan);\n}\n.camera-lens {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 20px;\n  height: 20px;\n  border-radius: 50%;\n  background: var(--accent-cyan);\n}\n.camera-flash {\n  position: absolute;\n  top: -8px;\n  right: -8px;\n  width: 12px;\n  height: 12px;\n  background: var(--accent-red);\n  border-radius: 2px;\n}\n.icon-trend {\n  position: relative;\n  width: 60px;\n  height: 60px;\n}\n.trend-line {\n  position: absolute;\n  height: 2px;\n  background: var(--gradient-primary);\n  transform-origin: left center;\n}\n.trend-line.line-1 {\n  width: 30px;\n  top: 40%;\n  left: 5px;\n  transform: rotate(-20deg);\n}\n.trend-line.line-2 {\n  width: 25px;\n  top: 50%;\n  left: 20px;\n  transform: rotate(15deg);\n}\n.trend-line.line-3 {\n  width: 20px;\n  top: 45%;\n  right: 10px;\n  transform: rotate(-10deg);\n}\n.trend-dot {\n  position: absolute;\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background: var(--accent-red);\n}\n.trend-dot.dot-1 {\n  top: 28px;\n  left: 8px;\n}\n.trend-dot.dot-2 {\n  top: 18px;\n  left: 30px;\n  background: var(--accent-purple);\n}\n.trend-dot.dot-3 {\n  top: 22px;\n  right: 8px;\n  background: var(--accent-cyan);\n}\n.icon-knowledge {\n  position: relative;\n  width: 60px;\n  height: 60px;\n}\n.knowledge-sources {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.source-node {\n  position: absolute;\n  width: 16px;\n  height: 16px;\n  border-radius: 4px;\n  border: 2px solid;\n}\n.source-node.doc {\n  top: 10px;\n  left: 10px;\n  background: var(--accent-red);\n  border-color: var(--accent-red);\n}\n.source-node.link {\n  top: 10px;\n  right: 10px;\n  background: var(--accent-purple);\n  border-color: var(--accent-purple);\n}\n.source-node.feed {\n  bottom: 10px;\n  left: 10px;\n  background: var(--accent-cyan);\n  border-color: var(--accent-cyan);\n}\n.knowledge-center {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 20px;\n  height: 20px;\n  border-radius: 50%;\n  background: var(--bg-primary);\n  border: 2px solid var(--text-primary);\n}\n.icon-viral {\n  position: relative;\n  width: 60px;\n  height: 60px;\n}\n.viral-bubble {\n  position: absolute;\n  border-radius: 50%;\n  background: var(--accent-red);\n  opacity: 0.6;\n}\n.viral-bubble.bubble-1 {\n  width: 20px;\n  height: 20px;\n  top: 10px;\n  left: 20px;\n  animation: pulse 2s infinite;\n}\n.viral-bubble.bubble-2 {\n  width: 16px;\n  height: 16px;\n  top: 30px;\n  left: 10px;\n  background: var(--accent-purple);\n  animation: pulse 2s infinite 0.5s;\n}\n.viral-bubble.bubble-3 {\n  width: 14px;\n  height: 14px;\n  bottom: 10px;\n  right: 15px;\n  background: var(--accent-cyan);\n  animation: pulse 2s infinite 1s;\n}\n.viral-pulse {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 30px;\n  height: 30px;\n  border: 2px solid var(--accent-red);\n  border-radius: 50%;\n  animation: pulse-ring 2s infinite;\n}\n@keyframes pulse {\n  0%,\n  100% {\n    transform: scale(1);\n    opacity: 0.6;\n  }\n  50% {\n    transform: scale(1.2);\n    opacity: 1;\n  }\n}\n@keyframes pulse-ring {\n  0% {\n    transform: translate(-50%, -50%) scale(1);\n    opacity: 1;\n  }\n  100% {\n    transform: translate(-50%, -50%) scale(2);\n    opacity: 0;\n  }\n}\n.icon-analytics {\n  position: relative;\n  width: 60px;\n  height: 60px;\n}\n.chart-bar {\n  position: absolute;\n  bottom: 10px;\n  width: 8px;\n  background: var(--accent-cyan);\n  border-radius: 2px;\n}\n.chart-bar.bar-1 {\n  left: 10px;\n  height: 20px;\n}\n.chart-bar.bar-2 {\n  left: 22px;\n  height: 35px;\n  background: var(--accent-purple);\n}\n.chart-bar.bar-3 {\n  left: 34px;\n  height: 25px;\n  background: var(--accent-red);\n}\n.chart-bar.bar-4 {\n  left: 46px;\n  height: 40px;\n}\n.chart-line {\n  position: absolute;\n  top: 15px;\n  left: 12px;\n  width: 40px;\n  height: 2px;\n  background: var(--gradient-primary);\n  transform: rotate(15deg);\n}\n.screenshots {\n  padding: 100px 0;\n  background: var(--bg-primary);\n}\n.screenshots-showcase {\n  display: flex;\n  flex-direction: column;\n  gap: 100px;\n}\n.screenshot-item {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 80px;\n  align-items: center;\n  margin: 80px 0;\n}\n.screenshot-item.reverse .screenshot-image {\n  order: 2;\n}\n.screenshot-item.reverse .screenshot-info {\n  order: 1;\n}\n.screenshot-image {\n  position: relative;\n}\n.screenshot-info h3 {\n  font-size: 2rem;\n  font-weight: 700;\n  color: var(--text-primary);\n  margin-bottom: 20px;\n}\n.screenshot-info p {\n  font-size: 1.1rem;\n  line-height: 1.6;\n  color: var(--text-secondary);\n}\n.demo-camera-roll {\n  border-radius: 16px;\n  overflow: hidden;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n  background: var(--bg-secondary);\n  border: 1px solid var(--border-color);\n  transition: all 0.4s ease;\n  max-width: 600px;\n  margin: 0 auto;\n}\n.demo-camera-roll:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 20px 60px rgba(255, 46, 77, 0.15);\n  border-color: rgba(255, 46, 77, 0.3);\n}\n.camera-header {\n  background: var(--bg-tertiary);\n  padding: 20px 24px;\n  border-bottom: 1px solid var(--border-color);\n}\n.camera-title-section {\n  margin-bottom: 16px;\n}\n.camera-title {\n  font-size: 18px;\n  font-weight: 700;\n  color: var(--text-primary);\n  margin: 0 0 4px 0;\n}\n.camera-tip {\n  font-size: 12px;\n  color: var(--text-secondary);\n}\n.camera-filter-tabs {\n  display: flex;\n  gap: 12px;\n}\n.filter-tab {\n  padding: 6px 12px;\n  background: var(--bg-secondary);\n  border: 1px solid var(--border-color);\n  border-radius: 20px;\n  font-size: 12px;\n  color: var(--text-secondary);\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n.filter-tab.active {\n  background: var(--accent-red);\n  color: white;\n  border-color: var(--accent-red);\n}\n.real-photo-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 20px;\n  padding: 24px;\n  background: var(--bg-primary);\n}\n.real-photo-card {\n  background: var(--bg-secondary);\n  border: 1px solid var(--border-color);\n  border-radius: 16px;\n  overflow: hidden;\n  transition: all 0.3s ease;\n  cursor: pointer;\n  position: relative;\n}\n.real-photo-card:hover {\n  background: var(--bg-tertiary);\n  box-shadow: 0 8px 24px rgba(6, 182, 212, 0.2);\n  transform: translateY(-4px);\n  border-color: var(--accent-cyan);\n}\n.real-photo-card.selected {\n  border-color: var(--accent-red);\n  box-shadow: 0 8px 24px rgba(255, 46, 77, 0.25);\n  transform: scale(1.02) translateY(-2px);\n}\n.photo-image-container {\n  position: relative;\n  height: 120px;\n  overflow: hidden;\n}\n.photo-placeholder {\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, #333 0%, #555 100%);\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.photo-placeholder.fashion {\n  background: linear-gradient(135deg, #ff6b7a 0%, #c44569 100%);\n}\n.photo-placeholder.lifestyle {\n  background: linear-gradient(135deg, #4834d4 0%, #686de0 100%);\n}\n.photo-placeholder::before {\n  content: \"📷\";\n  font-size: 24px;\n  opacity: 0.7;\n}\n.photo-placeholder.fashion::before {\n  content: \"👗\";\n}\n.photo-placeholder.lifestyle::before {\n  content: \"☕\";\n}\n.delete-button {\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  width: 28px;\n  height: 28px;\n  background: rgba(239, 68, 68, 0.9);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n  backdrop-filter: blur(4px);\n}\n.real-photo-card:hover .delete-button {\n  opacity: 1;\n}\n.photo-info {\n  padding: 16px;\n}\n.photo-filename {\n  font-size: 13px;\n  font-weight: 600;\n  color: var(--text-secondary);\n  margin-bottom: 10px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.photo-description {\n  font-size: 12px;\n  color: var(--text-muted);\n  line-height: 1.5;\n  margin-bottom: 12px;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n.photo-meta {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n.photo-date {\n  font-size: 11px;\n  color: var(--text-muted);\n}\n.photo-status {\n  font-size: 11px;\n  padding: 3px 8px;\n  border-radius: 12px;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n.photo-status.used {\n  background: rgba(255, 46, 77, 0.15);\n  color: var(--accent-red);\n}\n.photo-status.unused {\n  background: var(--bg-tertiary);\n  color: var(--text-muted);\n}\n.photo-tags {\n  display: flex;\n  gap: 6px;\n  flex-wrap: wrap;\n  margin-bottom: 10px;\n}\n.tag {\n  font-size: 10px;\n  padding: 2px 6px;\n  background: var(--bg-tertiary);\n  color: var(--text-muted);\n  border-radius: 8px;\n  font-weight: 500;\n}\n.photo-type-badge {\n  position: absolute;\n  top: 10px;\n  left: 10px;\n  font-size: 10px;\n  padding: 4px 8px;\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  border-radius: 12px;\n  font-weight: 600;\n  backdrop-filter: blur(4px);\n}\n.demo-persona-manager-compact {\n  background: var(--bg-secondary);\n  border: 1px solid var(--border-color);\n  border-radius: 16px;\n  overflow: hidden;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n  max-width: 600px;\n  margin: 0 auto;\n}\n.persona-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 16px;\n  padding: 24px;\n  background: var(--bg-primary);\n}\n.persona-card {\n  background: var(--bg-secondary);\n  border: 1px solid var(--border-color);\n  border-radius: 12px;\n  padding: 20px;\n  text-align: center;\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n.persona-card.active {\n  border-color: var(--accent-red);\n  background: rgba(255, 46, 77, 0.05);\n  transform: scale(1.02);\n}\n.persona-card-avatar {\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 20px;\n  font-weight: 700;\n  color: white;\n  margin: 0 auto 12px;\n}\n.persona-card-avatar.fiona {\n  background: var(--accent-red);\n}\n.persona-card-avatar.marcus {\n  background: var(--accent-purple);\n}\n.persona-card h4 {\n  font-size: 16px;\n  font-weight: 600;\n  color: var(--text-primary);\n  margin: 0 0 4px 0;\n}\n.persona-card p {\n  font-size: 12px;\n  color: var(--text-secondary);\n  margin: 0 0 12px 0;\n}\n.persona-knowledge-preview {\n  display: flex;\n  gap: 4px;\n  justify-content: center;\n  flex-wrap: wrap;\n}\n.knowledge-chip-mini {\n  font-size: 9px;\n  padding: 2px 6px;\n  background: var(--bg-tertiary);\n  color: var(--text-muted);\n  border-radius: 8px;\n  font-weight: 500;\n}\n.knowledge-chip-mini.active {\n  background: var(--accent-red);\n  color: white;\n}\n.persona-detail-summary {\n  padding: 24px;\n  background: var(--bg-tertiary);\n  border-top: 1px solid var(--border-color);\n}\n.knowledge-sources-summary {\n  margin-top: 16px;\n}\n.knowledge-chips {\n  display: flex;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n.knowledge-chip {\n  font-size: 11px;\n  padding: 4px 8px;\n  background: var(--bg-secondary);\n  color: var(--text-secondary);\n  border-radius: 12px;\n  font-weight: 500;\n  border: 1px solid var(--border-color);\n}\n.knowledge-chip.active {\n  background: var(--accent-red);\n  color: white;\n  border-color: var(--accent-red);\n}\n.demo-content-generation-compact {\n  background: var(--bg-secondary);\n  border: 1px solid var(--border-color);\n  border-radius: 16px;\n  overflow: hidden;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n  max-width: 600px;\n  margin: 0 auto;\n}\n.generation-process {\n  padding: 24px;\n  background: var(--bg-primary);\n}\n.process-steps {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 16px;\n}\n.step-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n  opacity: 0.5;\n  transition: all 0.3s ease;\n}\n.step-item.active {\n  opacity: 1;\n}\n.step-icon {\n  width: 40px;\n  height: 40px;\n  background: var(--bg-secondary);\n  border: 2px solid var(--border-color);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 18px;\n}\n.step-item.active .step-icon {\n  background: var(--accent-red);\n  border-color: var(--accent-red);\n  color: white;\n}\n.step-item span {\n  font-size: 12px;\n  color: var(--text-secondary);\n  font-weight: 500;\n}\n.step-arrow {\n  font-size: 16px;\n  color: var(--text-muted);\n}\n.generated-content-preview {\n  padding: 24px;\n  background: var(--bg-tertiary);\n  border-top: 1px solid var(--border-color);\n}\n.content-example {\n  background: var(--bg-primary);\n  border: 1px solid var(--border-color);\n  border-radius: 12px;\n  padding: 20px;\n}\n.content-header {\n  display: flex;\n  gap: 12px;\n  margin-bottom: 16px;\n}\n.persona-badge {\n  font-size: 11px;\n  padding: 4px 8px;\n  background: var(--accent-red);\n  color: white;\n  border-radius: 12px;\n  font-weight: 600;\n}\n.trend-badge {\n  font-size: 11px;\n  padding: 4px 8px;\n  background: var(--accent-cyan);\n  color: white;\n  border-radius: 12px;\n  font-weight: 600;\n}\n.content-body p {\n  font-size: 14px;\n  line-height: 1.5;\n  color: var(--text-primary);\n  margin: 0 0 16px 0;\n}\n.content-platforms {\n  display: flex;\n  gap: 8px;\n}\n.platform-icon {\n  font-size: 16px;\n  opacity: 0.7;\n}\n@media (max-width: 768px) {\n  .screenshots {\n    padding: 80px 0;\n  }\n  .screenshot-item {\n    grid-template-columns: 1fr;\n    gap: 40px;\n    margin: 60px 0;\n  }\n  .screenshot-item.reverse .screenshot-image {\n    order: 1;\n  }\n  .screenshot-item.reverse .screenshot-info {\n    order: 2;\n  }\n  .screenshot-info h3 {\n    font-size: 1.5rem;\n  }\n  .demo-camera-roll,\n  .demo-persona-manager-compact,\n  .demo-content-generation-compact {\n    max-width: 100%;\n  }\n  .real-photo-grid {\n    grid-template-columns: 1fr;\n  }\n  .persona-grid {\n    grid-template-columns: 1fr;\n  }\n}\n.cta {\n  padding: 100px 0;\n  background: var(--bg-secondary);\n  text-align: center;\n}\n.cta-content {\n  max-width: 800px;\n  margin: 0 auto;\n}\n.cta-title {\n  font-size: 40px;\n  font-weight: 700;\n  color: var(--text-primary);\n  margin-bottom: 16px;\n  line-height: 1.2;\n}\n.cta-subtitle {\n  font-size: 18px;\n  color: var(--text-secondary);\n  margin-bottom: 40px;\n  line-height: 1.5;\n}\n.cta-form {\n  margin-bottom: 24px;\n}\n.form-group {\n  display: flex;\n  gap: 16px;\n  justify-content: center;\n  max-width: 500px;\n  margin: 0 auto 24px;\n}\n.email-input {\n  flex: 1;\n  padding: 16px 20px;\n  background: var(--bg-primary);\n  border: 1px solid var(--border-color);\n  border-radius: 8px;\n  color: var(--text-primary);\n  font-size: 16px;\n  transition: all 0.3s ease;\n}\n.email-input:focus {\n  outline: none;\n  border-color: var(--accent-red);\n  box-shadow: 0 0 0 3px rgba(255, 46, 77, 0.1);\n}\n.email-input::placeholder {\n  color: var(--text-muted);\n}\n.form-note {\n  color: var(--text-secondary);\n  font-size: 14px;\n  margin: 0;\n}\n.success-message {\n  padding: 40px;\n  background: var(--bg-primary);\n  border: 1px solid var(--border-color);\n  border-radius: 12px;\n  max-width: 400px;\n  margin: 0 auto;\n}\n.success-message h3 {\n  font-size: 24px;\n  font-weight: 600;\n  color: var(--text-primary);\n  margin: 0 0 12px 0;\n}\n.success-message p {\n  font-size: 16px;\n  color: var(--text-secondary);\n  margin: 0;\n}\n@media (max-width: 768px) {\n  .cta {\n    padding: 80px 0;\n  }\n  .cta-title {\n    font-size: 32px;\n  }\n  .cta-subtitle {\n    font-size: 16px;\n  }\n  .form-group {\n    flex-direction: column;\n    gap: 12px;\n  }\n  .email-input {\n    font-size: 16px;\n  }\n}\n.footer {\n  padding: 60px 0 30px;\n  border-top: 1px solid var(--border-color);\n  background: var(--bg-primary);\n}\n.footer-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30px;\n}\n.footer-brand {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n.footer-logo {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n.footer-logo .logo-icon.small {\n  width: 32px;\n  height: 32px;\n}\n.footer-logo .logo-text {\n  font-size: 20px;\n  font-weight: 700;\n  color: var(--text-primary);\n}\n.footer-tagline {\n  color: var(--text-muted);\n  font-size: 14px;\n  margin: 0;\n}\n.footer-links {\n  display: flex;\n  gap: 32px;\n}\n.footer-links a {\n  color: var(--text-secondary);\n  text-decoration: none;\n  font-size: 14px;\n  transition: color 0.3s ease;\n}\n.footer-links a:hover {\n  color: var(--accent-red);\n}\n.footer-bottom {\n  text-align: center;\n  padding-top: 30px;\n  border-top: 1px solid var(--border-color);\n  color: var(--text-muted);\n  font-size: 14px;\n}\n.footer-bottom p {\n  margin: 0;\n}\n@media (max-width: 768px) {\n  .footer {\n    padding: 40px 0 20px;\n  }\n  .footer-content {\n    flex-direction: column;\n    gap: 30px;\n    text-align: center;\n  }\n  .footer-links {\n    gap: 20px;\n    flex-wrap: wrap;\n    justify-content: center;\n  }\n  .footer-bottom {\n    padding-top: 20px;\n  }\n}\n@property --tw-space-y-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-space-x-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-border-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-gradient-position {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-from {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-via {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-to {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-stops {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-via-stops {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-from-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 0%;\n}\n@property --tw-gradient-via-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 50%;\n}\n@property --tw-gradient-to-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-font-weight {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-inset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-ring-inset {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-offset-width {\n  syntax: \"<length>\";\n  inherits: false;\n  initial-value: 0px;\n}\n@property --tw-ring-offset-color {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: #fff;\n}\n@property --tw-ring-offset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-drop-shadow-size {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-duration {\n  syntax: \"*\";\n  inherits: false;\n}\n@keyframes pulse {\n  50% {\n    opacity: 0.5;\n  }\n}\n@layer properties {\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\n    *, ::before, ::after, ::backdrop {\n      --tw-space-y-reverse: 0;\n      --tw-space-x-reverse: 0;\n      --tw-border-style: solid;\n      --tw-gradient-position: initial;\n      --tw-gradient-from: #0000;\n      --tw-gradient-via: #0000;\n      --tw-gradient-to: #0000;\n      --tw-gradient-stops: initial;\n      --tw-gradient-via-stops: initial;\n      --tw-gradient-from-position: 0%;\n      --tw-gradient-via-position: 50%;\n      --tw-gradient-to-position: 100%;\n      --tw-font-weight: initial;\n      --tw-shadow: 0 0 #0000;\n      --tw-shadow-color: initial;\n      --tw-shadow-alpha: 100%;\n      --tw-inset-shadow: 0 0 #0000;\n      --tw-inset-shadow-color: initial;\n      --tw-inset-shadow-alpha: 100%;\n      --tw-ring-color: initial;\n      --tw-ring-shadow: 0 0 #0000;\n      --tw-inset-ring-color: initial;\n      --tw-inset-ring-shadow: 0 0 #0000;\n      --tw-ring-inset: initial;\n      --tw-ring-offset-width: 0px;\n      --tw-ring-offset-color: #fff;\n      --tw-ring-offset-shadow: 0 0 #0000;\n      --tw-blur: initial;\n      --tw-brightness: initial;\n      --tw-contrast: initial;\n      --tw-grayscale: initial;\n      --tw-hue-rotate: initial;\n      --tw-invert: initial;\n      --tw-opacity: initial;\n      --tw-saturate: initial;\n      --tw-sepia: initial;\n      --tw-drop-shadow: initial;\n      --tw-drop-shadow-color: initial;\n      --tw-drop-shadow-alpha: 100%;\n      --tw-drop-shadow-size: initial;\n      --tw-backdrop-blur: initial;\n      --tw-backdrop-brightness: initial;\n      --tw-backdrop-contrast: initial;\n      --tw-backdrop-grayscale: initial;\n      --tw-backdrop-hue-rotate: initial;\n      --tw-backdrop-invert: initial;\n      --tw-backdrop-opacity: initial;\n      --tw-backdrop-saturate: initial;\n      --tw-backdrop-sepia: initial;\n      --tw-duration: initial;\n    }\n  }\n}\n"], "names": [], "mappings": "AACA;EAkkFE;IACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAlkFJ;EAEE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA;IAAA;;;;;;;;;;EAAA;IAAA;;;;;;;;;;;AAFF;EA0CE;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAMA;;;;;;;;;;;EASA;;;;;;EAKA;;;;;EAIA;;;;;EAIA;;;;;;;EAKA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;;;;;;;EAAA;;;;;;;;;;;EAAA;;;;;;;;;;;EAUA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAGA;IACE;;;;IAEE;MAAgD;;;;;;EAKpD;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAGA;;;;EAGA;;;;;;EAAA;;;;;;EAAA;;;;;;EAAA;;;;;;EAGA;;;;EAAA;;;;EAGA;;;;;AAtLF;;AAAA;EA2LE;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAI3B;;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAIE;;;;;;EAOA;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAOA;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAMF;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;EAIA;;;;;EAMI;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAMzB;IAAyB;;;;;;;AAM7B;;;;;;;;;;;;;;;;;;;;AAwBA;;;;;;AAKA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;;;;;;AAUA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;AAIA;;;;;;;;;;;AAUA;;;;AAGA;;;;;;;;;;;;;;AAaA;;;;;;;;AAOA;;;;;;;;AAOA;;;;;;;AAMA;;;;;;;AAMA;;;;AAGA;;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAMA;;;;;AAIA;;;;;;AAKA;;;;;;;;;AAQA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;;AAMA;;;;;AAIA;;;;;;AAKA;;;;;;;;;;;;AAWA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;;;AAMA;;;;;;AAKA;;;;;;;;;AAQA;;;;;;;;;;;;;;AAWA;;;;;;;;;;;;AAYA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;EACE;;;;;;EAKA;;;;;AAIF;EACE;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;;;;;EAQA;;;;;AAIF;;;;;;;;;;;;;AAWA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;;;AAOA;;;;;AAIA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;;;;AAQA;;;;;;;;;;AAQA;;;;;;;;;;AAQA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;;;;AAOA;;;;;;;AAMA;;;;;;;;AAOA;;;;;;;;;;;AAUA;;;;;;;;;;;;AAWA;;;;;;AAKA;;;;AAGA;;;;;;AAKA;;;;;;;AAMA;;;;;;AAKA;EACE;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;AAIF;;;;;;;AAUA;;;;;;;;;;;AAmBA;;;;;;;;AAOA;;;;;;;;AAOA;;;;;;;AAMA;;;;;AAIA;;;;;;AAKA;;;;;;;;;;;AAkBA;;;;;;;AAMA;;;;;;AAKA;;;;;;;;;;AASA;;;;;AAIA;;;;;;;AAMA;;;;;AAIA;EACE;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;AAIF;;;;;;AAKA;;;;;AAIA;;;;;;;AAMA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;;AAMA;;;;;;AAKA;;;;AAGA;;;;;;;AAMA;;;;AAGA;;;;;;;;AAOA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;;AAMA;;;;AAGA;;;;;AAIA;;;;;;;;;;;;;AAYA;;;;;;AAKA;EACE;;;;EAGA;;;;;;EAKA;;;;;EAIA;;;;;;EAKA;;;;;EAIA;;;;;AAIF;;;;;AAIA;;;;;;;;AAOA;;;;;;;;AAOA;;;;;;AAKA;;;;;;;;AAOA;;;;;;;AAMA;;;;;;;;;AAQA;;;;AAGA;;;;;;;AAMA;;;;;;;;;;;AAUA;;;;;;AAKA;;;;;;;AAMA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;;;;AAQA;;;;;AAIA;;;;AAGA;;;;;;;;;;;;AAWA;;;;;;;AAMA;;;;;;;AAMA;;;;;;AAKA;;;;;;;;AAOA;;;;;;;;;;;AAUA;;;;;;;AAMA;;;;;AAIA;;;;AAGA;;;;;;;AAMA;;;;;AAIA;;;;;AAIA;EACE;;;;EAGA;;;;;EAIA;;;;;AAIF;;;;;;AAKA;;;;;;;;AAOA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;;;;AAQA;;;;;;;;;;;AAUA;;;;;;;;;;AASA;;;;;;AAKA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;;AAOA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;;;AAOA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;;;;;;AAWA;;;;;;AAKA;;;;;;;AAMA;;;;;;;;AAOA;;;;;;;;;AAQA;;;;;;;;;AAQA;;;;;;;;;;;;AAo6BA;;;;;;AA94BA;;;;;;;;;;;;AAUA;;;;;;AAKA;;;;;;;;AAOA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;;;;;AASA;;;;;AAIA;;;;;;AAKA;;;;;;;;AAOA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAMA;;;;;;AAKA;;;;;;;;;;;AAUA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;;;;AAMA;;;;;AAIA;;;;;AAIA;;;;;;;;;;;AAUA;;;;;;AAKA;;;;;;;;AAOA;;;;;;;;;;AASA;;;;;;;AAMA;;;;;;AAKA;;;;;;AAKA;;;;;;;;;;AASA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;;;;;;;;;;;;;;AAgBA;;;;AAGA;;;;AAGA;;;;;;;;;;AASA;;;;;;;;;;;AAUA;;;;;;;AAMA;;;;;AAIA;;;;;;;;;AAQA;;;;;AAIA;;;;;AAIA;;;;;;;AAMA;;;;;;;;;AAQA;;;;;;;;;;;;;;AAYA;;;;;;;;;;AASA;;;;;;;;AAOA;;;;;;;;;;AASA;;;;;;AAKA;;;;;;;;;;;;;AAYA;;;;AAGA;;;;AAGA;;;;;;;AAMA;;;;;;AAKA;;;;;;;AAMA;;;;;;;;;AAQA;;;;;AAIA;;;;;;AAKA;;;;AAGA;;;;;;AAKA;;;;;;;;;;AASA;;;;;;AAKA;;;;;;;;;;AASA;;;;;AAIA;;;;;;;AAMA;;;;;;;;;AAQA;;;;AAGA;;;;;;;;;;;;AAWA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;;AAMA;;;;;;AAKA;;;;;;;;;AAQA;;;;;;;;;AAQA;;;;;;;AAMA;;;;;AAIA;;;;;AAIA;EACE;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAKA;;;;;AAOF;;;;;;AAKA;;;;;AAIA;;;;;;;;AAOA;;;;;;;AAMA;;;;AAGA;;;;;;;;AAOA;;;;;;;;;;;AAUA;;;;;;AAKA;;;;AAGA;;;;;;AAKA;;;;;;;;;AAQA;;;;;;;AAMA;;;;;;AAKA;EACE;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;AAIF;;;;;;AAKA;;;;;;;AAMA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;;AAMA;;;;AAGA;;;;;;;;AAOA;;;;AAGA;EACE;;;;EAGA;;;;;;EAKA;;;;;;EAKA;;;;;AAIF;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA", "debugId": null}}]}