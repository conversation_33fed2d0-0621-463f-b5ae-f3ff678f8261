import { useTranslations } from "next-intl";

export default function FooterSection() {
  const tFooter = useTranslations("Footer");
  const tNav = useTranslations("Navigation");

  return (
    <footer className="footer bg-bg-secondary border-t border-border-color py-16">
      <div className="container">
        <div className="footer-content">
          <div className="footer-brand">
            <div className="footer-logo">
              <div className="logo-icon small">
                <div className="persona-layers">
                  <div className="persona-layer layer-1"></div>
                  <div className="persona-layer layer-2"></div>
                  <div className="persona-layer layer-3"></div>
                </div>
              </div>
              <span className="logo-text">PersonaRoll</span>
            </div>
            <p className="footer-tagline">Authentic Voices, Infinite Stories</p>
          </div>
          <div className="footer-links">
            <a href="#">{tNav("privacy")}</a>
            <a href="#">{tNav("terms")}</a>
            <a href="#">{tNav("contact")}</a>
            <a href="#">{tNav("twitter")}</a>
          </div>
        </div>

        {/* Footer Bottom */}
        <div className="footer-bottom border-t border-border-color pt-8">
          <p className="text-center text-text-muted">{tFooter("copyright")}</p>
        </div>
      </div>
    </footer>
  );
}
